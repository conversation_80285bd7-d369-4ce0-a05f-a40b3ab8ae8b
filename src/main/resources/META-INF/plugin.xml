<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>qhyu.asia.swcares</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>Swcares</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor url="https://www.yourcompany.com">YourCompany</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    Enter short description for your plugin here.<br>
    <em>most HTML tags may be used</em>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <toolWindow id="Swcares"
                    factoryClass="qhyu.asia.swcares.SwcaresToolWindow"
                    anchor="right"
                    icon="/icons/toolWindow.svg"/>
    </extensions>

    <!-- Actions -->
    <actions>
        <group id="SwcaresActionGroup" text="Swcares" description="Swcares plugin actions">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
            <action id="SwcaresLoginAction"
                    class="qhyu.asia.swcares.LoginAction"
                    text="登录 Chandao"
                    description="登录到 Chandao 系统并获取数据">
            </action>
        </group>
    </actions>
</idea-plugin>