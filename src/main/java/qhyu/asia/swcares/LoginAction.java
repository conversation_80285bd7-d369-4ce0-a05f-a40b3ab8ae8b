package qhyu.asia.swcares;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import org.jetbrains.annotations.NotNull;

/**
 * Action to handle login button click
 * Opens embedded browser dialog for user authentication
 */
public class LoginAction extends AnAction {
    
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }
        
        try {
            // Create and show login dialog with embedded browser
            WebLoginDialog loginDialog = new WebLoginDialog(project);
            boolean loginSuccess = loginDialog.showAndWaitForLogin();
            
            if (loginSuccess) {
                // Login successful, cookies are automatically captured
                Messages.showInfoMessage(project, 
                    "登录成功！Cookie已自动获取并保存。", 
                    "登录成功");
                
                // Optionally trigger data refresh or other actions
                refreshUserData(project);
            } else {
                Messages.showWarningDialog(project,
                    "登录已取消或失败。",
                    "登录取消");
            }
        } catch (Exception ex) {
            Messages.showErrorDialog(project, 
                "登录过程中发生错误: " + ex.getMessage(), 
                "登录错误");
        }
    }
    
    /**
     * Refresh user data after successful login
     */
    private void refreshUserData(Project project) {
        try {
            WebDataService dataService = new WebDataService();
            // This will use the automatically captured cookies
            String userData = dataService.fetchUserData();
            
            // Process and display the data as needed
            // You can implement your own data display logic here
            Messages.showInfoMessage(project, 
                "数据获取成功！", 
                "数据刷新");
        } catch (Exception ex) {
            Messages.showWarningDialog(project,
                "数据获取失败: " + ex.getMessage(),
                "数据获取错误");
        }
    }
}
