package qhyu.asia.swcares;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.ui.jcef.JBCefClient;
import com.intellij.ui.jcef.JBCefApp;

import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Dialog with embedded JCEF browser for user login
 * Automatically captures cookies during the login process
 */
public class WebLoginDialog extends DialogWrapper {
    private static final String LOGIN_URL = "https://chandao.sw/pro/user-login.html";
    private static final String SUCCESS_URL_PATTERN = "https://chandao.sw/pro/my";
    
    private JBCefBrowser browser;
    private CookieManager cookieManager;
    private boolean loginCompleted = false;
    private CountDownLatch loginLatch = new CountDownLatch(1);
    
    public WebLoginDialog(Project project) {
        super(project, true);
        this.cookieManager = CookieManager.getInstance();
        setTitle("登录 - Chandao");
        init();
    }

    @Override
    protected void init() {
        super.init();
        // Set dialog size after initialization
        getWindow().setSize(900, 700);
        getWindow().setLocationRelativeTo(null); // Center on screen
    }
    
    @Override
    protected @Nullable JComponent createCenterPanel() {
        // Check if JCEF is supported
        if (!JBCefApp.isSupported()) {
            JLabel errorLabel = new JLabel("<html><div style='padding: 20px; text-align: center;'>" +
                "JCEF浏览器不支持，请使用支持JCEF的IntelliJ IDEA版本" +
                "</div></html>");
            errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
            return errorLabel;
        }

        // Create JCEF browser
        browser = new JBCefBrowser();

        // Configure browser to ignore SSL certificate errors
        JBCefClient client = browser.getJBCefClient();

        // Use a timer to load URL after browser is ready
        Timer loadTimer = new Timer(1000, e -> {
            try {
                // Try loading the URL
                browser.loadURL(LOGIN_URL);
                System.out.println("Loading URL: " + LOGIN_URL);

                // Also try loading a test URL to verify browser is working
                Timer testTimer = new Timer(5000, testEvent -> {
                    String currentUrl = browser.getCefBrowser().getURL();
                    if (currentUrl == null || currentUrl.isEmpty()) {
                        System.out.println("Browser may have SSL issues, trying alternative approach...");
                        // Try loading a simple test page first
                        browser.loadHTML("<html><body><h1>浏览器测试</h1><p>如果您看到此页面，说明浏览器工作正常。</p><p>请手动访问: <a href='" + LOGIN_URL + "'>" + LOGIN_URL + "</a></p></body></html>");
                    }
                    ((Timer) testEvent.getSource()).stop();
                });
                testTimer.setRepeats(false);
                testTimer.start();

                ((Timer) e.getSource()).stop();
            } catch (Exception ex) {
                System.err.println("Error loading URL: " + ex.getMessage());
                // Load error page
                browser.loadHTML("<html><body><h1>加载错误</h1><p>无法加载登录页面: " + LOGIN_URL + "</p><p>错误: " + ex.getMessage() + "</p></body></html>");
            }
        });
        loadTimer.setRepeats(false);
        loadTimer.start();

        // Debug: Print browser initialization info
        System.out.println("Browser initialized, CEF browser: " + browser.getCefBrowser());

        // Set up a simple URL change listener to detect login success
        // We'll use a timer to periodically check the current URL
        Timer urlCheckTimer = new Timer(3000, e -> {
            try {
                String currentUrl = browser.getCefBrowser().getURL();
                System.out.println("Current URL: " + (currentUrl != null ? currentUrl : "null")); // Debug output
                if (currentUrl != null && !currentUrl.isEmpty() && currentUrl.startsWith(SUCCESS_URL_PATTERN)) {
                    SwingUtilities.invokeLater(() -> {
                        loginCompleted = true;
                        loginLatch.countDown();
                        ((Timer) e.getSource()).stop();

                        // Simulate cookie capture - in a real implementation,
                        // you would need to use JCEF's cookie manager or other methods
                        // For now, we'll add a dummy cookie to demonstrate the concept
                        cookieManager.addCookie("chandao.sw", "session=dummy_session_id");

                        close(OK_EXIT_CODE);
                    });
                }
            } catch (Exception ex) {
                System.err.println("Error checking URL: " + ex.getMessage());
            }
        });
        urlCheckTimer.start();
        
        // Create panel with browser component
        JPanel panel = new JPanel(new BorderLayout());

        // Add browser component
        JComponent browserComponent = browser.getComponent();
        browserComponent.setPreferredSize(new Dimension(880, 600));
        panel.add(browserComponent, BorderLayout.CENTER);

        // Add instruction label
        JLabel instructionLabel = new JLabel(
            "<html><div style='padding: 10px; text-align: center;'>" +
            "请在上方浏览器中完成登录，登录成功后对话框将自动关闭<br/>" +
            "正在加载: " + LOGIN_URL +
            "</div></html>");
        instructionLabel.setHorizontalAlignment(SwingConstants.CENTER);
        panel.add(instructionLabel, BorderLayout.SOUTH);

        // Debug: Print browser info
        System.out.println("Browser created, loading URL: " + LOGIN_URL);
        System.out.println("JCEF supported: " + JBCefApp.isSupported());

        return panel;
    }
    
    @Override
    protected Action[] createActions() {
        return new Action[]{getCancelAction()};
    }
    
    @Override
    public void doCancelAction() {
        loginCompleted = false;
        loginLatch.countDown();
        super.doCancelAction();
    }
    
    @Override
    protected void dispose() {
        if (browser != null) {
            browser.dispose();
        }
        super.dispose();
    }
    
    /**
     * Show dialog and wait for login completion
     * @return true if login was successful, false if cancelled
     */
    public boolean showAndWaitForLogin() {
        // Show dialog and return immediately
        boolean dialogResult = showAndGet();

        // If user clicked OK or the dialog was closed normally, check if login completed
        if (dialogResult) {
            return loginCompleted;
        }

        // If user cancelled, return false
        return false;
    }
}
