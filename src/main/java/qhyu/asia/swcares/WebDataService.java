package qhyu.asia.swcares;

import com.intellij.openapi.diagnostic.Logger;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Service for making web requests using captured cookies
 * Handles data fetching from authenticated endpoints
 */
public class WebDataService {
    private static final Logger LOG = Logger.getInstance(WebDataService.class);
    private static final String TARGET_URL = "https://chandao.sw/pro/my-work-task.html";
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    
    private final CookieManager cookieManager;
    
    public WebDataService() {
        this.cookieManager = CookieManager.getInstance();
    }
    
    /**
     * Fetch user data from the target URL using stored cookies
     * @return HTML content or JSON data from the endpoint
     * @throws IOException if request fails
     */
    public String fetchUserData() throws IOException {
        return fetchData(TARGET_URL);
    }
    
    /**
     * Fetch data from any URL using stored cookies
     * @param url The URL to fetch data from
     * @return Response content as string
     * @throws IOException if request fails
     */
    public String fetchData(String url) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            
            // Set headers
            httpGet.setHeader("User-Agent", USER_AGENT);
            httpGet.setHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br");
            httpGet.setHeader("Connection", "keep-alive");
            httpGet.setHeader("Upgrade-Insecure-Requests", "1");
            
            // Add cookies
            String cookies = cookieManager.getCookiesForUrl(url);
            if (!cookies.isEmpty()) {
                httpGet.setHeader("Cookie", cookies);
                LOG.info("Using cookies for request: " + cookies);
            } else {
                LOG.warn("No cookies available for URL: " + url);
                throw new IOException("No authentication cookies available. Please login first.");
            }
            
            // Execute request
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getCode();
                LOG.info("Response status code: " + statusCode);
                
                if (statusCode == 200) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        try {
                            String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                            LOG.info("Successfully fetched data, content length: " + content.length());
                            return content;
                        } catch (Exception parseEx) {
                            throw new IOException("Failed to parse response content", parseEx);
                        }
                    } else {
                        throw new IOException("Empty response from server");
                    }
                } else if (statusCode == 401 || statusCode == 403) {
                    throw new IOException("Authentication failed. Please login again. Status: " + statusCode);
                } else if (statusCode == 302 || statusCode == 301) {
                    // Handle redirects - might indicate need to login again
                    String location = response.getFirstHeader("Location") != null ? 
                        response.getFirstHeader("Location").getValue() : "unknown";
                    throw new IOException("Redirected to: " + location + ". Please login again.");
                } else {
                    throw new IOException("HTTP request failed with status: " + statusCode);
                }
            }
        }
    }
    
    /**
     * Test if current cookies are still valid
     * @return true if cookies are valid and can access protected resources
     */
    public boolean testCookieValidity() {
        try {
            String content = fetchData("https://chandao.sw/pro/my/");
            // Check if the response contains login form or user data
            return !content.contains("user-login") && content.contains("my-work");
        } catch (Exception e) {
            LOG.info("Cookie validity test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Parse and extract specific data from HTML content
     * This is a basic example - you should implement your own parsing logic
     * based on the actual structure of the target website
     */
    public String parseUserTasks(String htmlContent) {
        // Basic example of data extraction
        // You should implement proper HTML parsing using libraries like Jsoup
        // or implement JSON parsing if the endpoint returns JSON
        
        if (htmlContent.contains("task-list") || htmlContent.contains("work-task")) {
            // Extract task information
            StringBuilder tasks = new StringBuilder();
            tasks.append("发现任务数据:\n");
            
            // This is a simplified example - implement actual parsing logic
            if (htmlContent.contains("task-title")) {
                tasks.append("- 找到任务标题相关内容\n");
            }
            if (htmlContent.contains("task-status")) {
                tasks.append("- 找到任务状态相关内容\n");
            }
            
            return tasks.toString();
        } else {
            return "未找到任务数据，可能需要重新登录";
        }
    }
}
