package qhyu.asia.swcares;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Tool window for displaying Chandao data
 */
public class SwcaresToolWindow implements ToolWindowFactory {
    
    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        SwcaresToolWindowContent toolWindowContent = new SwcaresToolWindowContent(project);
        Content content = ContentFactory.getInstance().createContent(toolWindowContent.getContentPanel(), "", false);
        toolWindow.getContentManager().addContent(content);
    }
    
    /**
     * Content panel for the tool window
     */
    public static class SwcaresToolWindowContent {
        private final Project project;
        private JPanel contentPanel;
        private JTextArea dataTextArea;
        private JButton loginButton;
        private JButton refreshButton;
        private JLabel statusLabel;
        
        public SwcaresToolWindowContent(Project project) {
            this.project = project;
            initializeUI();
        }
        
        private void initializeUI() {
            contentPanel = new JPanel(new BorderLayout());
            
            // Top panel with buttons
            JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
            
            loginButton = new JButton("登录");
            loginButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    performLogin();
                }
            });
            
            refreshButton = new JButton("刷新数据");
            refreshButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    refreshData();
                }
            });
            refreshButton.setEnabled(false);
            
            statusLabel = new JLabel("未登录");
            
            topPanel.add(loginButton);
            topPanel.add(refreshButton);
            topPanel.add(Box.createHorizontalStrut(20));
            topPanel.add(new JLabel("状态: "));
            topPanel.add(statusLabel);
            
            // Center panel with data display
            dataTextArea = new JTextArea();
            dataTextArea.setEditable(false);
            dataTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            dataTextArea.setText("请先点击登录按钮进行身份验证...");
            
            JScrollPane scrollPane = new JScrollPane(dataTextArea);
            scrollPane.setPreferredSize(new Dimension(400, 300));
            
            contentPanel.add(topPanel, BorderLayout.NORTH);
            contentPanel.add(scrollPane, BorderLayout.CENTER);
        }
        
        private void performLogin() {
            SwingUtilities.invokeLater(() -> {
                statusLabel.setText("登录中...");
                loginButton.setEnabled(false);
                
                try {
                    WebLoginDialog loginDialog = new WebLoginDialog(project);
                    boolean loginSuccess = loginDialog.showAndGet();
                    
                    if (loginSuccess) {
                        statusLabel.setText("已登录");
                        refreshButton.setEnabled(true);
                        dataTextArea.setText("登录成功！点击'刷新数据'按钮获取最新数据。");
                    } else {
                        statusLabel.setText("登录失败");
                        dataTextArea.setText("登录失败或已取消。请重试。");
                    }
                } catch (Exception ex) {
                    statusLabel.setText("登录错误");
                    dataTextArea.setText("登录过程中发生错误: " + ex.getMessage());
                } finally {
                    loginButton.setEnabled(true);
                }
            });
        }
        
        private void refreshData() {
            SwingUtilities.invokeLater(() -> {
                statusLabel.setText("获取数据中...");
                refreshButton.setEnabled(false);
                dataTextArea.setText("正在获取数据，请稍候...");
                
                // Use background thread for network request
                new Thread(() -> {
                    try {
                        WebDataService dataService = new WebDataService();
                        String rawData = dataService.fetchUserData();
                        String parsedData = dataService.parseUserTasks(rawData);
                        
                        SwingUtilities.invokeLater(() -> {
                            statusLabel.setText("数据已更新");
                            dataTextArea.setText("=== 用户任务数据 ===\n\n" + parsedData + 
                                "\n\n=== 原始数据 (前1000字符) ===\n\n" + 
                                (rawData.length() > 1000 ? rawData.substring(0, 1000) + "..." : rawData));
                            refreshButton.setEnabled(true);
                        });
                    } catch (Exception ex) {
                        SwingUtilities.invokeLater(() -> {
                            statusLabel.setText("数据获取失败");
                            dataTextArea.setText("数据获取失败: " + ex.getMessage() + 
                                "\n\n可能需要重新登录。");
                            refreshButton.setEnabled(true);
                        });
                    }
                }).start();
            });
        }
        
        public JPanel getContentPanel() {
            return contentPanel;
        }
    }
}
