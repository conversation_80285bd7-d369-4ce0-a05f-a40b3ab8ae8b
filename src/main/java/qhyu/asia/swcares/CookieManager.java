package qhyu.asia.swcares;

import com.intellij.openapi.application.PathManager;
import com.intellij.openapi.diagnostic.Logger;

import java.io.*;
import java.net.HttpCookie;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages cookies for web requests
 * Provides persistent storage and automatic cookie handling
 */
public class CookieManager {
    private static final Logger LOG = Logger.getInstance(CookieManager.class);
    private static final String COOKIE_FILE_NAME = "swcares_cookies.dat";
    
    private static CookieManager instance;
    private final Map<String, List<HttpCookie>> cookieStore;
    private final Path cookieFilePath;
    
    private CookieManager() {
        this.cookieStore = new ConcurrentHashMap<>();
        this.cookieFilePath = Paths.get(PathManager.getPluginsPath(), "swcares", COOKIE_FILE_NAME);
        loadCookiesFromFile();
    }
    
    public static synchronized CookieManager getInstance() {
        if (instance == null) {
            instance = new CookieManager();
        }
        return instance;
    }
    
    /**
     * Add cookie from Set-Cookie header
     */
    public void addCookie(String url, String setCookieHeader) {
        try {
            List<HttpCookie> cookies = HttpCookie.parse(setCookieHeader);
            String domain = extractDomain(url);
            
            for (HttpCookie cookie : cookies) {
                if (cookie.getDomain() == null) {
                    cookie.setDomain(domain);
                }
                addCookie(domain, cookie);
            }
            
            saveCookiesToFile();
        } catch (Exception e) {
            LOG.warn("Failed to parse cookie: " + setCookieHeader, e);
        }
    }
    
    /**
     * Add cookie to store
     */
    public void addCookie(String domain, HttpCookie cookie) {
        cookieStore.computeIfAbsent(domain, k -> new ArrayList<>()).add(cookie);
        LOG.info("Added cookie: " + cookie.getName() + " for domain: " + domain);
    }
    
    /**
     * Get cookies for a specific URL
     */
    public String getCookiesForUrl(String url) {
        String domain = extractDomain(url);
        List<HttpCookie> cookies = getCookiesForDomain(domain);
        
        if (cookies.isEmpty()) {
            return "";
        }
        
        StringBuilder cookieHeader = new StringBuilder();
        for (HttpCookie cookie : cookies) {
            if (!isExpired(cookie)) {
                if (cookieHeader.length() > 0) {
                    cookieHeader.append("; ");
                }
                cookieHeader.append(cookie.getName()).append("=").append(cookie.getValue());
            }
        }
        
        return cookieHeader.toString();
    }
    
    /**
     * Get all cookies for a domain
     */
    private List<HttpCookie> getCookiesForDomain(String domain) {
        List<HttpCookie> result = new ArrayList<>();
        
        // Check exact domain match
        List<HttpCookie> exactMatch = cookieStore.get(domain);
        if (exactMatch != null) {
            result.addAll(exactMatch);
        }
        
        // Check parent domains
        for (Map.Entry<String, List<HttpCookie>> entry : cookieStore.entrySet()) {
            String cookieDomain = entry.getKey();
            if (domain.endsWith(cookieDomain) && !domain.equals(cookieDomain)) {
                result.addAll(entry.getValue());
            }
        }
        
        return result;
    }
    
    /**
     * Check if cookie is expired
     */
    private boolean isExpired(HttpCookie cookie) {
        return cookie.hasExpired();
    }
    
    /**
     * Extract domain from URL
     */
    private String extractDomain(String url) {
        try {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                int start = url.indexOf("://") + 3;
                int end = url.indexOf("/", start);
                if (end == -1) {
                    end = url.length();
                }
                String host = url.substring(start, end);
                
                // Remove port if present
                int portIndex = host.indexOf(":");
                if (portIndex != -1) {
                    host = host.substring(0, portIndex);
                }
                
                return host;
            }
        } catch (Exception e) {
            LOG.warn("Failed to extract domain from URL: " + url, e);
        }
        return "localhost";
    }
    
    /**
     * Save cookies to file for persistence
     */
    private void saveCookiesToFile() {
        try {
            Files.createDirectories(cookieFilePath.getParent());
            
            try (ObjectOutputStream oos = new ObjectOutputStream(
                    new FileOutputStream(cookieFilePath.toFile()))) {
                oos.writeObject(new HashMap<>(cookieStore));
            }
            
            LOG.info("Cookies saved to file: " + cookieFilePath);
        } catch (Exception e) {
            LOG.warn("Failed to save cookies to file", e);
        }
    }
    
    /**
     * Load cookies from file
     */
    @SuppressWarnings("unchecked")
    private void loadCookiesFromFile() {
        if (!Files.exists(cookieFilePath)) {
            return;
        }
        
        try (ObjectInputStream ois = new ObjectInputStream(
                new FileInputStream(cookieFilePath.toFile()))) {
            Map<String, List<HttpCookie>> loadedCookies = 
                (Map<String, List<HttpCookie>>) ois.readObject();
            
            // Filter out expired cookies
            for (Map.Entry<String, List<HttpCookie>> entry : loadedCookies.entrySet()) {
                List<HttpCookie> validCookies = new ArrayList<>();
                for (HttpCookie cookie : entry.getValue()) {
                    if (!isExpired(cookie)) {
                        validCookies.add(cookie);
                    }
                }
                if (!validCookies.isEmpty()) {
                    cookieStore.put(entry.getKey(), validCookies);
                }
            }
            
            LOG.info("Cookies loaded from file: " + cookieFilePath);
        } catch (Exception e) {
            LOG.warn("Failed to load cookies from file", e);
        }
    }
    
    /**
     * Clear all cookies
     */
    public void clearCookies() {
        cookieStore.clear();
        try {
            Files.deleteIfExists(cookieFilePath);
        } catch (Exception e) {
            LOG.warn("Failed to delete cookie file", e);
        }
    }
}
